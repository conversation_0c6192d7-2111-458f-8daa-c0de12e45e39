<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Dental Smyle - Case Study | Jaykee Aba-a</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700|space-grotesk:500" rel="stylesheet">
  <script src="https://unpkg.com/lucide@latest"></script>
  <style>
    body{font-family:'Inter',sans-serif;background:#0e0e10;color:#e5e7eb;}
    .animate{opacity:0;transform:translateY(40px);transition:all .8s cubic-bezier(.22,1,.36,1);}
    .animate.show{opacity:1;transform:none;}
    .divider{border-top:1px solid rgba(255,255,255,.08);}
    .outline{outline:1px solid rgba(255,255,255,.08);}
    ::selection{background:#6366f1;color:#fff;}
    a{transition:.3s;}
    a:hover{color:#a5b4fc;}
    .gradient-text{background:linear-gradient(90deg,#6366f1,#8b5cf6 60%,#ec4899);-webkit-background-clip:text;color:transparent;}
  </style>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="min-h-screen flex flex-col">
  <!-- NAV -->
  <header class="fixed top-0 inset-x-0 z-50 backdrop-blur-sm bg-black/30">
    <div class="mx-auto max-w-7xl flex items-center justify-between px-6 py-4">
      <a href="v2.html" class="text-lg tracking-tight font-semibold gradient-text">JK</a>
      <nav class="hidden sm:flex gap-6 text-sm">
        <a href="v2.html#work">Work</a><a href="v2.html#services">Services</a><a href="v2.html#about">About</a><a href="v2.html#contact">Contact</a>
      </nav>
      <a href="v2.html#contact" class="sm:block hidden text-xs font-medium px-4 py-2 outline rounded-md hover:bg-white/5">Let's talk</a>
      <button id="menuBtn" class="sm:hidden">
        <i data-lucide="menu" class="stroke-[1.5] w-6 h-6"></i>
      </button>
    </div>
    <!-- mobile -->
    <div id="mobileNav" class="sm:hidden px-6 pt-2 pb-4 space-y-2 hidden">
      <a href="v2.html#work" class="block">Work</a>
      <a href="v2.html#services" class="block">Services</a>
      <a href="v2.html#about" class="block">About</a>
      <a href="v2.html#contact" class="block">Contact</a>
    </div>
  </header>

  <!-- HERO SECTION -->
  <section class="relative px-6 pt-32 pb-16">
    <div class="mx-auto max-w-4xl">
      <a href="v2.html" class="inline-flex items-center gap-2 text-sm opacity-70 hover:opacity-100 mb-8 animate">
        <i data-lucide="arrow-left" class="stroke-[1.5] w-4 h-4"></i>
        Back to portfolio
      </a>
      <h1 class="text-4xl sm:text-5xl font-semibold tracking-tight animate delay-[100ms]">Dental Smyle</h1>
      <p class="mt-4 text-lg opacity-80 animate delay-[200ms]">A modern dental practice website providing comprehensive dental care services with a focus on patient comfort and advanced technology</p>
      <div class="flex gap-4 mt-6 animate delay-[250ms]">
        <a href="https://dental-smyle.vercel.app/" target="_blank" class="inline-flex items-center gap-2 px-4 py-2 bg-white text-black rounded-md text-sm font-medium transition-colors">
          Visit website <i data-lucide="external-link" class="stroke-[1.5] w-4 h-4"></i>
        </a>
        <a href="#project-details" class="inline-flex items-center gap-2 px-4 py-2 outline rounded-md text-sm font-medium hover:bg-white/5 transition-colors">
          View details <i data-lucide="arrow-down" class="stroke-[1.5] w-4 h-4"></i>
        </a>
      </div>
    </div>
  </section>

  <!-- PROJECT IMAGE -->
  <section class="px-6 pb-16">
    <div class="mx-auto max-w-4xl">
      <img src="dental.png" alt="Dental Smyle Website" class="w-full rounded-lg outline animate delay-[300ms]">
    </div>
  </section>

  <!-- PROJECT DETAILS -->
  <section id="project-details" class="px-6 pb-24">
    <div class="mx-auto max-w-4xl grid md:grid-cols-3 gap-12">
      <!-- Project Info -->
      <div class="md:col-span-2 space-y-8">
        <div class="animate delay-[400ms]">
          <h2 class="text-2xl font-semibold mb-4">Project Overview</h2>
          <p class="text-sm opacity-80 leading-relaxed mb-4">
            Bright Smile Dental is a modern dental practice that combines cutting-edge technology with personalized treatment to provide exceptional dental care. This project aimed to create a professional, welcoming website that showcases their comprehensive services while making it easy for patients to book appointments and learn about treatments.
          </p>
          <p class="text-sm opacity-80 leading-relaxed">
            The design emphasizes trust, professionalism, and patient comfort through clean layouts, calming colors, and intuitive navigation. The website features detailed service information, patient testimonials, and streamlined appointment booking to enhance the patient experience from first visit to ongoing care.
          </p>
        </div>

        <div class="animate delay-[500ms]">
          <h3 class="text-xl font-semibold mb-4">Key Features</h3>
          <ul class="space-y-3 text-sm">
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Comprehensive Service Pages:</strong> Detailed information about general dentistry, cosmetic dentistry, orthodontics, oral surgery, pediatric dentistry, and emergency care</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Online Appointment Booking:</strong> Streamlined booking system with service selection and contact form integration</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Patient Testimonials:</strong> Real reviews from verified patients showcasing treatment experiences and results</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Practice Statistics:</strong> Highlighting 15+ years experience, 5000+ happy patients, and 98% success rate</span>
            </li>
            <li class="flex gap-3 items-start">
              <i data-lucide="check" class="stroke-[1.5] w-4 h-4 text-indigo-400 mt-0.5 flex-shrink-0"></i>
              <span><strong>Emergency Care Information:</strong> 24/7 emergency dental services with clear contact information and procedures</span>
            </li>
          </ul>
        </div>

        <div class="animate delay-[600ms]">
          <h3 class="text-xl font-semibold mb-4">Design Approach</h3>
          <p class="text-sm opacity-80 leading-relaxed mb-4">
            The visual design emphasizes trust, cleanliness, and professionalism through a clean white and blue color palette that evokes feelings of health and reliability. The layout prioritizes patient comfort and ease of use, with clear navigation and prominent call-to-action buttons for appointment booking.
          </p>
          <p class="text-sm opacity-80 leading-relaxed">
            Typography choices focus on readability and accessibility, ensuring all patients can easily access important information. High-quality imagery showcases the modern facility and happy patients, building confidence in the practice's expertise and care quality.
          </p>
        </div>
      </div>

      <!-- Project Meta -->
      <div class="space-y-8">
        <div class="p-6 outline rounded-lg animate delay-[700ms]">
          <h3 class="font-semibold mb-4">Project Details</h3>
          <div class="space-y-3 text-sm">
            <div>
              <span class="opacity-70">Client:</span>
              <div class="font-medium">Bright Smile Dental</div>
            </div>

            <div>
              <span class="opacity-70">Year:</span>
              <div class="font-medium">2025</div>
            </div>
            <div>
              <span class="opacity-70">Role:</span>
              <div class="font-medium">UI/UX Design, Front-end Development</div>
            </div>
          </div>
        </div>

        <div class="p-6 outline rounded-lg animate delay-[800ms]">
          <h3 class="font-semibold mb-4">Technologies Used</h3>
          <div class="flex flex-wrap gap-2">
            <span class="px-3 py-1 bg-indigo-500/20 text-indigo-300 rounded-full text-xs">CSS (42.3%)</span>
            <span class="px-3 py-1 bg-indigo-500/20 text-indigo-300 rounded-full text-xs">HTML (31.3%)</span>
            <span class="px-3 py-1 bg-indigo-500/20 text-indigo-300 rounded-full text-xs">JavaScript (26.4%)</span>
          </div>
        </div>

        <div class="p-6 outline rounded-lg animate delay-[900ms]">
          <h3 class="font-semibold mb-4">Results</h3>
          <div class="space-y-3 text-sm">
            <div class="flex justify-between">
              <span class="opacity-70">Performance:</span>
              <span class="font-medium text-green-400">80</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">Accessibility:</span>
              <span class="font-medium text-green-400">82</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">Best Practices:</span>
              <span class="font-medium text-green-400">100</span>
            </div>
            <div class="flex justify-between">
              <span class="opacity-70">SEO:</span>
              <span class="font-medium text-green-400">91</span>
            </div>

          </div>
        </div>
      </div>
  </section>

  <!-- NEXT PROJECT -->
  <section class="px-6 pb-24">
    <div class="mx-auto max-w-4xl">
      <div class="divider mb-12"></div>
      <div class="flex justify-between items-center animate">
        <div>
          <h3 class="text-xl font-semibold">Next Project</h3>
          <p class="text-sm opacity-70 mt-1">HyperMart E-commerce Platform</p>
        </div>
        <a href="hypermart-details.html" class="flex items-center gap-2 text-sm hover:text-indigo-400">
          View case study <i data-lucide="arrow-right" class="stroke-[1.5] w-4 h-4"></i>
        </a>
      </div>
    </div>
  </section>

  <footer class="mt-auto py-10 px-6 text-center text-[11px] opacity-60">
    © <span id="year"></span> Jaykee Aba-a. Built & designed by me.
  </footer>

  <script>
    // Lucide
    lucide.createIcons({attrs:{'stroke-width':1.5}});

    // Mobile nav toggle
    const menuBtn=document.getElementById('menuBtn'), mobileNav=document.getElementById('mobileNav');
    menuBtn.addEventListener('click',()=>mobileNav.classList.toggle('hidden'));

    // Scroll animations
    const observer=new IntersectionObserver(entries=>{
      entries.forEach(e=>{if(e.isIntersecting){e.target.classList.add('show');observer.unobserve(e.target);}})
    },{threshold:.12});
    document.querySelectorAll('.animate').forEach(el=>observer.observe(el));

    // year
    document.getElementById('year').textContent=new Date().getFullYear();
  </script>
</body>
</html>
